<template>
  <div class="device-display">
    <div class="display-header">
      <h3>设备画面</h3>
      <div class="display-controls">
        <el-select v-model="viewMode" placeholder="显示模式" style="width: 120px; margin-right: 10px;">
          <el-option label="网格视图" value="grid"></el-option>
          <el-option label="列表视图" value="list"></el-option>
        </el-select>
        <el-select v-model="devicesPerPage" placeholder="每页设备数" style="width: 120px;">
          <el-option label="4个" value="4"></el-option>
          <el-option label="8个" value="8"></el-option>
          <el-option label="12个" value="12"></el-option>
          <el-option label="16个" value="16"></el-option>
        </el-select>
      </div>
    </div>
    
    <!-- 设备筛选和搜索 -->
    <div class="device-filter" v-if="allDevices.length > 0">
      <el-input 
        v-model="searchQuery"
        placeholder="搜索设备名称或ID"
        prefix-icon="el-icon-search"
        style="width: 300px; margin-right: 10px;"
      ></el-input>
      <el-select v-model="filterStatus" placeholder="筛选状态" style="width: 120px;">
        <el-option label="全部" value="all"></el-option>
        <el-option label="在线" value="online"></el-option>
        <el-option label="离线" value="offline"></el-option>
      </el-select>
    </div>
    
    <!-- 设备展示区域 -->
    <div 
      class="devices-container"
      :class="{ 'grid-view': viewMode === 'grid', 'list-view': viewMode === 'list' }"
    >
      <div v-if="filteredDevices.length === 0" class="no-devices">
        <el-empty description="暂无设备可展示"></el-empty>
      </div>
      
      <!-- 分页处理 -->
      <div v-for="device in paginatedDevices" :key="device.id" class="device-card">
        <div class="device-card-header">
          <div class="device-card-name">{{ device.name || device.id }}</div>
          <div class="device-card-status" :class="device.status">
            <i class="el-icon-circle-check" v-if="device.status === 'online'"></i>
            <i class="el-icon-circle-close" v-else></i>
            {{ device.status === 'online' ? '在线' : '离线' }}
          </div>
        </div>
        
        <!-- 手机样式展示 -->
        <div class="phone-container">
          <div class="phone-frame">
            <div class="phone-screen" :class="{ 'screen-off': device.status === 'offline' }">
              <!-- 这里将通过WebSocket接收scrcpy流 -->
              <div v-if="device.status === 'online'" class="screen-content">
                <div class="screen-placeholder">设备画面</div>
              </div>
              <div v-else class="screen-off-content">
                <i class="el-icon-video-camera-off"></i>
                <div>设备离线</div>
              </div>
            </div>
            <!-- 手机底部按钮 -->
            <div class="phone-bottom">
              <div class="home-button"></div>
            </div>
          </div>
        </div>
        
        <!-- 分辨率和帧率信息 -->
        <div class="device-specs" v-if="device.status === 'online'">
          <div>分辨率: 1080x2340</div>
          <div>帧率: 60fps</div>
        </div>
        

      </div>
    </div>
    
    <!-- 分页控件 -->
    <div v-if="filteredDevices.length > 0" class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="devicesPerPage"
        layout="total, sizes, prev, pager, next, jumper"
        :total="filteredDevices.length"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue';

export default {
  name: 'DeviceDisplay',
  props: {
    // 接收父组件传递的设备列表
    devices: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    // 视图模式
    const viewMode = ref('grid'); // grid 或 list
    // 分页设置
    const currentPage = ref(1);
    const devicesPerPage = ref(4);
    // 搜索和筛选
    const searchQuery = ref('');
    const filterStatus = ref('all');
    
    // 默认设备数据（如果没有从父组件接收到）
    const allDevices = ref([
      { id: 'JEF-AN00', name: '华为手机', status: 'online' },
      { id: 'SM-G998B', name: '三星手机', status: 'online' },
      { id: 'iPhone13', name: '苹果手机', status: 'offline' },
      { id: 'MI12', name: '小米手机', status: 'online' },
      { id: 'OPPO-Reno7', name: 'OPPO手机', status: 'online' },
      { id: 'VIVO-X70', name: 'VIVO手机', status: 'offline' }
    ]);
    
    // 监听父组件传递的设备列表变化
    watch(() => props.devices, (newDevices) => {
      if (newDevices && newDevices.length > 0) {
        allDevices.value = [...newDevices];
      }
    }, { immediate: true });
    
    // 筛选设备
    const filteredDevices = computed(() => {
      let result = [...allDevices.value];
      
      // 按状态筛选
      if (filterStatus.value !== 'all') {
        result = result.filter(device => device.status === filterStatus.value);
      }
      
      // 按搜索关键词筛选
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        result = result.filter(device => 
          (device.name && device.name.toLowerCase().includes(query)) ||
          device.id.toLowerCase().includes(query)
        );
      }
      
      // 按状态排序，在线设备在前
      result.sort((a, b) => {
        if (a.status === 'online' && b.status !== 'online') return -1;
        if (a.status !== 'online' && b.status === 'online') return 1;
        return 0;
      });
      
      return result;
    });
    
    // 分页后的设备
    const paginatedDevices = computed(() => {
      const startIndex = (currentPage.value - 1) * devicesPerPage.value;
      const endIndex = startIndex + parseInt(devicesPerPage.value);
      return filteredDevices.value.slice(startIndex, endIndex);
    });
    
    // 分页处理函数
    const handleSizeChange = (size) => {
      devicesPerPage.value = size;
      currentPage.value = 1; // 重置到第一页
    };
    
    const handleCurrentChange = (current) => {
      currentPage.value = current;
    };
    
    
    
    return {
      viewMode,
      currentPage,
      devicesPerPage,
      searchQuery,
      filterStatus,
      allDevices,
      filteredDevices,
      paginatedDevices,
      handleSizeChange,
      handleCurrentChange
    };
  }
};
</script>

<style scoped>
.device-display {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.display-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.display-header h3 {
  margin: 0;
  color: #303133;
}

.display-controls {
  display: flex;
  align-items: center;
}

.device-filter {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.devices-container {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 16px;
}

.no-devices {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

/* 网格视图 */
.grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

/* 列表视图 */
.list-view {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.list-view .device-card {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;
}

.list-view .device-card-header {
  width: 150px;
  margin-right: 20px;
}

.list-view .phone-container {
  flex: 1;
  display: flex;
  justify-content: center;
}

.list-view .device-controls {
  width: 250px;
}

.device-card {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.device-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.device-card-name {
  font-weight: 500;
  color: #303133;
}

.device-card-status {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.device-card-status.online {
  color: #67c23a;
}

.device-card-status.offline {
  color: #f56c6c;
}

/* 手机样式 */
.phone-container {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.phone-frame {
  width: 220px;
  height: 400px;
  border: 8px solid #333;
  border-radius: 30px;
  overflow: hidden;
  background-color: #000;
  position: relative;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.phone-frame::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 20px;
  background-color: #333;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.phone-screen {
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.phone-screen.screen-off {
  background-color: #000;
}

.screen-content {
  text-align: center;
  color: #303133;
}

.screen-placeholder {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
}

.screen-info {
  font-size: 12px;
  color: #606266;
}

.screen-off-content {
  color: #606266;
  text-align: center;
}

.screen-off-content i {
  font-size: 48px;
  margin-bottom: 10px;
}

.phone-bottom {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 50px;
  border: 2px solid #666;
  border-radius: 50%;
  background-color: #333;
}

.device-specs {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  padding: 8px 0;
  font-size: 12px;
  color: #606266;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-top: 8px;
}

.device-controls {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding-top: 10px;
  border-top: 1px solid #e4e7ed;
}
</style>