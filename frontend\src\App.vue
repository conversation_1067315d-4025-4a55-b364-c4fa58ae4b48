<template>
  <div class="app-container">
    <!-- 顶部菜单栏 -->
    <header class="top-menu">
      <div class="menu-title">设备群控系统</div>
      <div class="menu-options">
        <el-button type="primary" @click="switchView('deviceView')">设备视图</el-button>
        <el-button @click="switchView('scriptView')">脚本管理</el-button>
        <el-button @click="showSettings">设置</el-button>
      </div>
    </header>

    <!-- 主内容区域 -->
    <main class="main-content">
      <!-- 设备视图 -->
      <template v-if="currentView === 'deviceView'">
        <!-- 左侧设备管理 -->
        <aside class="left-panel">
          <DeviceManager v-model:devices="allDevices" />
        </aside>

        <!-- 中间设备展示 -->
        <section class="center-panel">
          <DeviceDisplay :devices="allDevices" />
        </section>

        <!-- 右侧脚本日志 -->
        <aside class="right-panel">
          <ScriptLog />
        </aside>
      </template>

      <!-- 脚本管理视图 -->
      <template v-else-if="currentView === 'scriptView'">
        <section class="script-manager-container">
          <ScriptManager />
        </section>
      </template>
    </main>
  </div>
</template>

<script>
import { ref } from 'vue';
import DeviceManager from './components/DeviceManager.vue';
import DeviceDisplay from './components/DeviceDisplay.vue';
import ScriptLog from './components/ScriptLog.vue';
import ScriptManager from './components/ScriptManager.vue';

export default {
  name: 'App',
  components: {
    DeviceManager,
    DeviceDisplay,
    ScriptLog,
    ScriptManager
  },
  setup() {
    const currentView = ref('deviceView');
    
    // 共享的设备数据
    const allDevices = ref([
      { id: 'JEF-AN00', name: '华为手机', status: 'online' },
      { id: 'SM-G998B', name: '三星手机', status: 'online' },
      { id: 'iPhone13', name: '苹果手机', status: 'offline' },
      { id: 'MI12', name: '小米手机', status: 'online' },
      { id: 'OPPO-Reno7', name: 'OPPO手机', status: 'online' },
      { id: 'VIVO-X70', name: 'VIVO手机', status: 'offline' }
    ]);

    const switchView = (view) => {
      currentView.value = view;
    };

    const showSettings = () => {
      // 显示设置对话框
      console.log('显示设置');
    };

    return {
      currentView,
      switchView,
      showSettings,
      allDevices
    };
  }
};
</script>

<style scoped>
.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.top-menu {
  height: 60px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  z-index: 100;
}

.menu-title {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
}

.menu-options {
  display: flex;
  gap: 10px;
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.left-panel {
  width: 250px;
  background-color: #fff;
  border-right: 1px solid #e4e7ed;
  overflow-y: auto;
}

.center-panel {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.right-panel {
  width: 300px;
  background-color: #fff;
  border-left: 1px solid #e4e7ed;
  overflow-y: auto;
}

.script-manager-container {
  flex: 1;
  overflow: hidden;
}
</style>