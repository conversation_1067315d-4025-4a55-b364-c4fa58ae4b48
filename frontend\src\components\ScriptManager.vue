<template>
  <div class="script-manager">
    <div class="manager-header">
      <h2>脚本管理</h2>
      <el-button type="primary" @click="showAddScriptDialog">添加脚本</el-button>
    </div>
    
    <div class="script-list">
      <el-card v-for="script in scripts" :key="script.id" class="script-card">
        <div class="script-header">
          <div class="script-info">
            <div class="script-name">{{ script.name }}</div>
            <div class="script-meta">
              <span>创建于: {{ formatDate(script.createdAt) }}</span>
              <span v-if="script.runTime">运行时间: {{ script.runTime }}</span>
              <span v-if="script.status" :class="['status', script.status]">{{ script.status === 'running' ? '运行中' : '已停止' }}</span>
            </div>
          </div>
          <div class="script-actions">
            <el-button type="text" @click="editScript(script)">编辑</el-button>
            <el-button type="text" @click="deleteScript(script.id)">删除</el-button>
            <el-button v-if="script.status !== 'running'" type="primary" size="small" @click="showDeviceSelection(script.id)">运行</el-button>
            <el-button v-else type="danger" size="small" @click="stopScript(script.id)">停止</el-button>
          </div>
        </div>
        <div class="script-content">
          <pre>{{ script.content }}</pre>
        </div>
        <div v-if="script.scheduledTime" class="schedule-info">
          <el-tag type="info">定时任务: {{ script.scheduledTime }}</el-tag>
        </div>
        <div v-if="script.runCount" class="run-count-info">
          <el-tag type="warning">运行次数: {{ script.runCount }}</el-tag>
        </div>
      </el-card>
    </div>
    
    <!-- 添加/编辑脚本对话框 -->
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑脚本' : '添加脚本'" width="700px">
      <el-form ref="scriptForm" :model="currentScript" label-width="80px" :rules="formRules">
        <el-form-item label="脚本名称" prop="name">
          <el-input v-model="currentScript.name" placeholder="请输入脚本名称"></el-input>
        </el-form-item>
        <el-form-item label="脚本内容" prop="content">
          <el-input 
            v-model="currentScript.content" 
            type="textarea" 
            placeholder="请输入Python脚本内容"
            :rows="10"
            style="font-family: monospace; font-size: 13px;"
          ></el-input>
        </el-form-item>
        <el-form-item label="运行次数" prop="runCount">
          <el-input-number 
            v-model="currentScript.runCount" 
            :min="1" 
            :max="999" 
            placeholder="设置脚本运行次数"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="定时设置">
          <el-radio-group v-model="currentScript.scheduleType">
            <el-radio label="immediate">立即执行</el-radio>
            <el-radio label="scheduled">定时执行</el-radio>
            <el-radio label="periodic">周期性执行</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="currentScript.scheduleType === 'scheduled'" label="开始时间">
          <el-date-picker 
            v-model="currentScript.startTime" 
            type="datetime" 
            placeholder="选择开始时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          ></el-date-picker>
        </el-form-item>
        <el-form-item v-if="currentScript.scheduleType === 'scheduled'" label="结束时间">
          <el-date-picker 
            v-model="currentScript.endTime" 
            type="datetime" 
            placeholder="选择结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          ></el-date-picker>
        </el-form-item>
        <el-form-item v-if="currentScript.scheduleType === 'periodic'" label="周期设置">
          <el-select v-model="currentScript.periodType" placeholder="选择周期类型">
            <el-option label="每分钟" value="minute"></el-option>
            <el-option label="每小时" value="hour"></el-option>
            <el-option label="每天" value="day"></el-option>
            <el-option label="每周" value="week"></el-option>
            <el-option label="每月" value="month"></el-option>
          </el-select>
          <el-input-number 
            v-model="currentScript.periodValue" 
            :min="1" 
            style="margin-left: 10px;" 
            placeholder="间隔值"
          ></el-input-number>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveScript">保存</el-button>
      </div>
    </el-dialog>
    
    <!-- 设备选择对话框 -->
    <el-dialog v-model="deviceDialogVisible" title="选择运行设备" width="400px">
      <div class="device-selection">
        <div v-if="allDevices.length === 0" class="no-devices">
          <el-empty description="暂无可用设备"></el-empty>
        </div>
        <el-checkbox-group v-model="selectedDevices">
          <el-checkbox 
            v-for="device in allDevices" 
            :key="device.id" 
            :label="device.id" 
            :disabled="device.status !== 'online'"
            class="device-item"
          >
            <div class="device-info">
              <div class="device-name">{{ device.name || device.id }}</div>
              <div class="device-type">{{ device.type || 'Android设备' }}</div>
            </div>
            <span v-if="device.status !== 'online'" class="device-offline"> (离线)</span>
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <div class="dialog-footer">
        <el-button @click="deviceDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="confirmRunScript" 
          :disabled="selectedDevices.length === 0"
        >
          确定运行
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, onMounted, inject } from 'vue';
import useWebSocket from '../utils/websocket';
import { ElMessageBox, ElMessage } from 'element-plus';

export default {
  name: 'ScriptManager',
  setup() {
    const scripts = ref([]);
    const dialogVisible = ref(false);
    const isEdit = ref(false);
    const scriptForm = ref(null);
    const currentScript = ref({
      id: '',
      name: '',
      content: '',
      createdAt: '',
      runTime: '',
      status: '',
      runCount: 1,
      scheduleType: 'immediate',
      startTime: '',
      endTime: '',
      periodType: 'minute',
      periodValue: 1
    });

    const formRules = ref({
      name: [
        { required: true, message: '请输入脚本名称', trigger: 'blur' },
        { max: 50, message: '脚本名称不能超过50个字符', trigger: 'blur' }
      ],
      content: [
        { required: true, message: '请输入脚本内容', trigger: 'blur' }
      ],
      runCount: [
        { required: true, message: '请设置运行次数', trigger: 'change' }
      ]
    });

    const { connected, executeScript, handleScriptStatusUpdate, sendMessage } = useWebSocket();
    const allDevices = inject('allDevices', ref([]));
    
    // 用于选择设备运行脚本的对话框相关状态
    const deviceDialogVisible = ref(false);
    const selectedDevices = ref([]);
    const scriptToRun = ref(null);
    
    // 记录日志
    const log = (message, level = 'info', deviceId = '') => {
      // 通过WebSocket发送日志消息
      sendMessage('script_log', {
        level,
        message,
        scriptName: '脚本管理',
        deviceId
      });
    };

    // 初始化脚本列表
    const initScripts = () => {
      // 模拟数据，实际应该从后端获取
      scripts.value = [
        {
          id: '1',
          name: '抖音自动点赞脚本',
          content: '# 抖音自动点赞脚本\nimport time\nimport random\n\nprint("开始执行抖音自动点赞脚本")\nfor i in range(10):\n    print(f"点赞第{i+1}个视频")\n    time.sleep(random.randint(1, 3))\nprint("脚本执行完成")',
          createdAt: '2023-11-15 10:30:00',
          runTime: '00:00:25',
          status: 'stopped',
          runCount: 5
        },
        {
          id: '2',
          name: '抖音自动评论脚本',
          content: '# 抖音自动评论脚本\nimport time\nimport random\n\ncomments = ["太棒了！", "真不错！", "学到了！", "点赞支持！"]\n\nprint("开始执行抖音自动评论脚本")\nfor i in range(5):\n    comment = random.choice(comments)\n    print(f"评论: {comment}")\n    time.sleep(random.randint(2, 4))\nprint("脚本执行完成")',
          createdAt: '2023-11-16 14:20:00',
          status: 'stopped',
          runCount: 3,
          scheduledTime: '2023-11-20 18:00:00'
        }
      ];
    };

    // 显示添加脚本对话框
    const showAddScriptDialog = () => {
      isEdit.value = false;
      currentScript.value = {
        id: Date.now().toString(),
        name: '',
        content: '',
        createdAt: new Date().toLocaleString(),
        runTime: '',
        status: '',
        runCount: 1,
        scheduleType: 'immediate',
        startTime: '',
        endTime: '',
        periodType: 'minute',
        periodValue: 1
      };
      dialogVisible.value = true;
    };

    // 编辑脚本
    const editScript = (script) => {
      isEdit.value = true;
      currentScript.value = { ...script };
      dialogVisible.value = true;
    };

    // 删除脚本
    const deleteScript = (scriptId) => {
      ElMessageBox.confirm('确定要删除这个脚本吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        scripts.value = scripts.value.filter(script => script.id !== scriptId);
        ElMessage.success('脚本删除成功');
        log(`脚本已删除，ID: ${scriptId}`, 'info');
      }).catch(() => {
        ElMessage.info('已取消删除');
      });
    };

    // 保存脚本
    const saveScript = () => {
      if (scriptForm.value) {
        scriptForm.value.validate((valid) => {
          if (valid) {
            if (isEdit.value) {
              // 编辑现有脚本
              const index = scripts.value.findIndex(script => script.id === currentScript.value.id);
              if (index !== -1) {
                scripts.value[index] = { ...currentScript.value };
              }
              log(`脚本已更新: ${currentScript.value.name}`, 'info');
            } else {
              // 添加新脚本
              scripts.value.push({ ...currentScript.value });
              log(`脚本已添加: ${currentScript.value.name}`, 'info');
            }
            
            // 格式化定时信息
            if (currentScript.value.scheduleType === 'scheduled' && currentScript.value.startTime) {
              scripts.value[scripts.value.length - 1].scheduledTime = currentScript.value.startTime;
            } else if (currentScript.value.scheduleType === 'periodic') {
              const periodText = {
                minute: '分钟',
                hour: '小时',
                day: '天',
                week: '周',
                month: '月'
              };
              scripts.value[scripts.value.length - 1].scheduledTime = `每${currentScript.value.periodValue}${periodText[currentScript.value.periodType]}`;
            }
            
            dialogVisible.value = false;
            ElMessage.success(isEdit.value ? '脚本更新成功' : '脚本添加成功');
          }
        });
      }
    };

    // 显示设备选择对话框
    const showDeviceSelection = (scriptId) => {
      const script = scripts.value.find(s => s.id === scriptId);
      if (script) {
        scriptToRun.value = scriptId;
        selectedDevices.value = [];
        deviceDialogVisible.value = true;
      }
    };

    // 确认运行脚本
    const confirmRunScript = () => {
      if (scriptToRun.value && selectedDevices.value.length > 0) {
        const script = scripts.value.find(s => s.id === scriptToRun.value);
        if (script) {
          // 更新脚本状态为运行中
          script.status = 'running';
          
          // 调用WebSocket发送运行脚本命令到后端
          executeScript(scriptToRun.value, selectedDevices.value);
          
          const deviceNames = selectedDevices.value.map(deviceId => {
            const device = allDevices.value.find(d => d.id === deviceId);
            return device ? device.name || device.id : deviceId;
          }).join(', ');
          
          ElMessage.success(`脚本 "${script.name}" 已开始运行`);
          log(`脚本 "${script.name}" 已开始运行，目标设备：${deviceNames}`, 'info');
          
          // 模拟运行过程中的日志
          for (let i = 1; i <= (script.runCount || 1); i++) {
            setTimeout(() => {
              log(`脚本 "${script.name}" 第${i}次执行中...`, 'info');
            }, i * 5000);
          }
          
          // 模拟运行结束（实际应该通过WebSocket接收脚本结束的消息）
          setTimeout(() => {
            const scriptToUpdate = scripts.value.find(s => s.id === scriptToRun.value);
            if (scriptToUpdate) {
              scriptToUpdate.status = 'stopped';
              scriptToUpdate.runTime = '00:01:30'; // 模拟运行时间
              ElMessage.success(`脚本 "${scriptToUpdate.name}" 已运行完成`);
              log(`脚本 "${scriptToUpdate.name}" 已运行完成，共运行 ${scriptToUpdate.runCount} 次，总耗时 ${scriptToUpdate.runTime}`, 'success');
            }
          }, (script.runCount || 1) * 5000 + 5000); // 根据运行次数调整模拟运行时间
          
          deviceDialogVisible.value = false;
          selectedDevices.value = [];
          scriptToRun.value = null;
        }
      } else {
        ElMessage.warning('请至少选择一个设备');
      }
    };

    // 停止脚本
    const stopScript = (scriptId) => {
      const script = scripts.value.find(s => s.id === scriptId);
      if (script) {
        // 调用WebSocket发送停止脚本命令到后端
        const { stopScript: wsStopScript } = useWebSocket();
        wsStopScript(scriptId);
        
        // 更新脚本状态为已停止
        script.status = 'stopped';
        ElMessage.success(`脚本 "${script.name}" 已停止`);
        log(`脚本 "${script.name}" 已停止`, 'warning');
      }
    };

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN');
    };

    // 监听脚本状态更新
    const handleStatusUpdate = (data) => {
      const { scriptId, status, runTime } = data;
      const script = scripts.value.find(s => s.id === scriptId);
      if (script) {
        script.status = status;
        if (runTime) {
          script.runTime = runTime;
        }
      }
    };

    // 组件挂载时初始化
    onMounted(() => {
      initScripts();
      handleScriptStatusUpdate(handleStatusUpdate);
      log('脚本管理页面加载完成', 'info');
    });

    return {
      scripts,
      dialogVisible,
      isEdit,
      currentScript,
      scriptForm,
      formRules,
      deviceDialogVisible,
      selectedDevices,
      scriptToRun,
      allDevices,
      showAddScriptDialog,
      editScript,
      deleteScript,
      saveScript,
      showDeviceSelection,
      confirmRunScript,
      stopScript,
      formatDate
    };
  }
};
</script>

<style scoped>
.script-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  background-color: #f5f5f5;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.manager-header h2 {
  margin: 0;
  color: #303133;
}

.script-list {
  flex: 1;
  overflow-y: auto;
}

.script-card {
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.script-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.script-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.script-info {
  flex: 1;
}

.script-name {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.script-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #606266;
}

.status {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status.running {
  background-color: #f0f9ff;
  color: #409eff;
}

.status.stopped {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.script-actions {
  display: flex;
  gap: 8px;
}

.script-content {
  background-color: #f8f8f8;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  margin-bottom: 10px;
}

.script-content pre {
  margin: 0;
  font-size: 13px;
  color: #303133;
  white-space: pre-wrap;
  word-break: break-all;
}

.schedule-info,
.run-count-info {
  margin-top: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

.device-selection {
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.device-item:last-child {
  border-bottom: none;
}

.device-info {
  flex: 1;
}

.device-name {
  font-weight: 500;
  color: #303133;
}

.device-type {
  font-size: 12px;
  color: #606266;
  margin-top: 2px;
}

.device-offline {
  color: #f56c6c;
  font-size: 12px;
}
</style>