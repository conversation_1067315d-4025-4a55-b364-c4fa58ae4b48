<template>
  <div class="script-log">
    <div class="log-header">
      <h3>脚本日志</h3>
      <div class="log-controls">
        <el-button type="text" @click="clearLogs" size="small">清空日志</el-button>
        <el-button type="text" @click="exportLogs" size="small">导出日志</el-button>
      </div>
    </div>
    
    <!-- 日志过滤 -->
    <div class="log-filter">
      <el-select v-model="logLevelFilter" placeholder="日志级别" style="width: 100px; margin-right: 10px;">
        <el-option label="全部" value="all"></el-option>
        <el-option label="信息" value="info"></el-option>
        <el-option label="成功" value="success"></el-option>
        <el-option label="警告" value="warning"></el-option>
        <el-option label="错误" value="error"></el-option>
      </el-select>
      <el-input 
        v-model="logSearch" 
        placeholder="搜索日志内容" 
        prefix-icon="el-icon-search" 
        style="flex: 1;"
      ></el-input>
    </div>
    
    <!-- 日志列表 -->
    <div class="log-content">
      <div v-if="filteredLogs.length === 0" class="no-logs">
        <el-empty description="暂无日志信息"></el-empty>
      </div>
      <div 
        v-for="(log, index) in filteredLogs" 
        :key="index" 
        class="log-item" 
        :class="`log-${log.level}`"
      >
        <div class="log-time">{{ formatTime(log.timestamp) }}</div>
        <div class="log-level-icon">{{ getLevelIcon(log.level) }}</div>
        <div class="log-message">
          <div v-if="log.scriptName" class="script-name">{{ log.scriptName }}</div>
          <div class="log-content-text">{{ log.message }}</div>
          <div v-if="log.deviceId" class="device-info">设备: {{ log.deviceId }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue';
import useWebSocket from '../utils/websocket';

export default {
  name: 'ScriptLog',
  setup() {
    const logs = ref([]);
    const logLevelFilter = ref('all');
    const logSearch = ref('');
    
    const { handleScriptLog } = useWebSocket();
    
    // 模拟初始日志数据
    const initLogs = () => {
      logs.value = [
        {
          timestamp: new Date().toISOString(),
          level: 'info',
          message: '系统启动成功',
          scriptName: '',
          deviceId: ''
        },
        {
          timestamp: new Date(Date.now() - 60000).toISOString(),
          level: 'success',
          message: '抖音自动点赞脚本执行完成',
          scriptName: '抖音自动点赞脚本',
          deviceId: 'JEF-AN00'
        },
        {
          timestamp: new Date(Date.now() - 120000).toISOString(),
          level: 'error',
          message: '无法连接到设备，请检查ADB连接',
          scriptName: '抖音自动评论脚本',
          deviceId: 'SM-G998B'
        }
      ];
    };
    
    // 根据过滤条件获取日志
    const filteredLogs = computed(() => {
      let filtered = [...logs.value];
      
      // 按日志级别过滤
      if (logLevelFilter.value !== 'all') {
        filtered = filtered.filter(log => log.level === logLevelFilter.value);
      }
      
      // 按搜索关键词过滤
      if (logSearch.value) {
        const searchTerm = logSearch.value.toLowerCase();
        filtered = filtered.filter(log => 
          (log.message && log.message.toLowerCase().includes(searchTerm)) ||
          (log.scriptName && log.scriptName.toLowerCase().includes(searchTerm)) ||
          (log.deviceId && log.deviceId.toLowerCase().includes(searchTerm))
        );
      }
      
      // 按时间倒序排序
      return filtered.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    });
    
    // 添加日志
    const addLog = (logData) => {
      const log = {
        timestamp: new Date().toISOString(),
        level: 'info',
        message: '',
        scriptName: '',
        deviceId: '',
        ...logData
      };
      logs.value.push(log);
      
      // 保持日志数量在合理范围内
      if (logs.value.length > 1000) {
        logs.value.shift();
      }
      
      // 自动滚动到底部
      setTimeout(() => {
        const logContainer = document.querySelector('.log-content');
        if (logContainer) {
          logContainer.scrollTop = logContainer.scrollHeight;
        }
      }, 100);
    };
    
    // 清空日志
    const clearLogs = () => {
      logs.value = [];
    };
    
    // 导出日志
    const exportLogs = () => {
      const logText = logs.value.map(log => 
        `${formatTime(log.timestamp)} [${log.level.toUpperCase()}] ${log.scriptName ? `[${log.scriptName}] ` : ''}${log.deviceId ? `[${log.deviceId}] ` : ''}${log.message}`
      ).join('\n');
      
      const blob = new Blob([logText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `script_log_${new Date().toISOString().replace(/[:.]/g, '-')}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    };
    
    // 格式化时间
    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    };
    
    // 获取日志级别图标
    const getLevelIcon = (level) => {
      const icons = {
        info: '📝',
        success: '✅',
        warning: '⚠️',
        error: '❌'
      };
      return icons[level] || '📝';
    };
    
    // 监听脚本日志
    const handleLog = (data) => {
      addLog({
        level: data.level || 'info',
        message: data.message,
        scriptName: data.scriptName,
        deviceId: data.deviceId
      });
    };
    
    // 组件挂载时初始化
    initLogs();
    handleScriptLog(handleLog);
    
    return {
      logs,
      logLevelFilter,
      logSearch,
      filteredLogs,
      addLog,
      clearLogs,
      exportLogs,
      formatTime,
      getLevelIcon
    };
  }
};
</script>

<style scoped>
.script-log {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
}

.log-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.log-controls {
  display: flex;
  gap: 8px;
}

.log-filter {
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  gap: 10px;
}

.log-content {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
}

.log-item {
  padding: 8px 12px;
  margin-bottom: 8px;
  background-color: #fff;
  border-radius: 4px;
  border-left: 3px solid #909399;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  transition: all 0.3s ease;
}

.log-item:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.log-info {
  border-left-color: #409eff;
}

.log-success {
  border-left-color: #67c23a;
}

.log-warning {
  border-left-color: #e6a23c;
}

.log-error {
  border-left-color: #f56c6c;
}

.log-time {
  font-size: 12px;
  color: #909399;
  min-width: 170px;
}

.log-level-icon {
  font-size: 16px;
  min-width: 20px;
  text-align: center;
}

.log-message {
  flex: 1;
}

.script-name {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.log-content-text {
  font-size: 13px;
  color: #606266;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
}

.device-info {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.no-logs {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>