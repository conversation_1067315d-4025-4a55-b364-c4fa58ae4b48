# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/helper-string-parser@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz"
  integrity sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==

"@babel/helper-validator-identifier@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz"
  integrity sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==

"@babel/parser@^7.28.4":
  version "7.28.4"
  resolved "https://registry.npmjs.org/@babel/parser/-/parser-7.28.4.tgz"
  integrity sha512-yZbBqeM6TkpP9du/I2pUZnJsRMGGvOuIrhjzC1AwHwW+6he4mni6Bp/m8ijn0iOuZuPI2BfkCoSRunpyjnrQKg==
  dependencies:
    "@babel/types" "^7.28.4"

"@babel/types@^7.28.4":
  version "7.28.4"
  resolved "https://registry.npmjs.org/@babel/types/-/types-7.28.4.tgz"
  integrity sha512-bkFqkLhh3pMBUQQkpVgWDWq/lqzc2678eUyDlTBhRqhCHFguYYGM0Efga7tYk4TogG/3x0EEl66/OQ+WGbWB/Q==
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@ctrl/tinycolor@^3.4.1":
  version "3.6.1"
  resolved "https://registry.npmjs.org/@ctrl/tinycolor/-/tinycolor-3.6.1.tgz"
  integrity sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==

"@element-plus/icons-vue@^2.3.1":
  version "2.3.2"
  resolved "https://registry.npmjs.org/@element-plus/icons-vue/-/icons-vue-2.3.2.tgz"
  integrity sha512-OzIuTaIfC8QXEPmJvB4Y4kw34rSXdCJzxcD1kFStBvr8bK6X1zQAYDo0CNMjojnfTqRQCJ0I7prlErcoRiET2A==

"@floating-ui/core@^1.7.3":
  version "1.7.3"
  resolved "https://registry.npmjs.org/@floating-ui/core/-/core-1.7.3.tgz"
  integrity sha512-sGnvb5dmrJaKEZ+LDIpguvdX3bDlEllmv4/ClQ9awcmCZrlx5jQyyMWFM5kBI+EyNOCDDiKk8il0zeuX3Zlg/w==
  dependencies:
    "@floating-ui/utils" "^0.2.10"

"@floating-ui/dom@^1.0.1":
  version "1.7.4"
  resolved "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.7.4.tgz"
  integrity sha512-OOchDgh4F2CchOX94cRVqhvy7b3AFb+/rQXyswmzmGakRfkMgoWVjfnLWkRirfLEfuD4ysVW16eXzwt3jHIzKA==
  dependencies:
    "@floating-ui/core" "^1.7.3"
    "@floating-ui/utils" "^0.2.10"

"@floating-ui/utils@^0.2.10":
  version "0.2.10"
  resolved "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.10.tgz"
  integrity sha512-aGTxbpbg8/b5JfU1HXSrbH3wXZuLPJcNEcZQFMxLs3oSzgtVu6nFPkbbGGUvBcUjKV2YyB9Wxxabo+HEH9tcRQ==

"@jridgewell/sourcemap-codec@^1.5.5":
  version "1.5.5"
  resolved "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.5.tgz"
  integrity sha512-cYQ9310grqxueWbl+WuIUIaiUaDcj7WOq5fVhEljNVgRfOUhY9fy2zTvfoqWsnebh8Sl70VScFbICvJnLKB0Og==

"@oxc-project/runtime@0.92.0":
  version "0.92.0"
  resolved "https://registry.npmjs.org/@oxc-project/runtime/-/runtime-0.92.0.tgz"
  integrity sha512-Z7x2dZOmznihvdvCvLKMl+nswtOSVxS2H2ocar+U9xx6iMfTp0VGIrX6a4xB1v80IwOPC7dT1LXIJrY70Xu3Jw==

"@oxc-project/types@=0.93.0":
  version "0.93.0"
  resolved "https://registry.npmjs.org/@oxc-project/types/-/types-0.93.0.tgz"
  integrity sha512-yNtwmWZIBtJsMr5TEfoZFDxIWV6OdScOpza/f5YxbqUMJk+j6QX3Cf3jgZShGEFYWQJ5j9mJ6jM0tZHu2J9Yrg==

"@popperjs/core@npm:@sxzz/popperjs-es@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@sxzz/popperjs-es/-/popperjs-es-2.11.7.tgz"
  integrity sha512-Ccy0NlLkzr0Ex2FKvh2X+OyERHXJ88XJ1MXtsI9y9fGexlaXaVTPzBCRBwIxFkORuOb+uBqeu+RqnpgYTEZRUQ==

"@rolldown/binding-win32-x64-msvc@1.0.0-beta.41":
  version "1.0.0-beta.41"
  resolved "https://registry.npmjs.org/@rolldown/binding-win32-x64-msvc/-/binding-win32-x64-msvc-1.0.0-beta.41.tgz"
  integrity sha512-UlpxKmFdik0Y2VjZrgUCgoYArZJiZllXgIipdBRV1hw6uK45UbQabSTW6Kp6enuOu7vouYWftwhuxfpE8J2JAg==

"@rolldown/pluginutils@1.0.0-beta.29":
  version "1.0.0-beta.29"
  resolved "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.29.tgz"
  integrity sha512-NIJgOsMjbxAXvoGq/X0gD7VPMQ8j9g0BiDaNjVNVjvl+iKXxL3Jre0v31RmBYeLEmkbj2s02v8vFTbUXi5XS2Q==

"@rolldown/pluginutils@1.0.0-beta.41":
  version "1.0.0-beta.41"
  resolved "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.41.tgz"
  integrity sha512-ycMEPrS3StOIeb87BT3/+bu+blEtyvwQ4zmo2IcJQy0Rd1DAAhKksA0iUZ3MYSpJtjlPhg0Eo6mvVS6ggPhRbw==

"@socket.io/component-emitter@~3.1.0":
  version "3.1.2"
  resolved "https://registry.npmjs.org/@socket.io/component-emitter/-/component-emitter-3.1.2.tgz"
  integrity sha512-9BCxFwvbGg/RsZK9tjXd8s4UcwR0MWeFQ1XEKIQVVvAGJyINdrqKMcTRyLoK8Rse1GjzLV9cwjWV1olXRWEXVA==

"@types/lodash-es@*", "@types/lodash-es@^4.17.12":
  version "4.17.12"
  resolved "https://registry.npmjs.org/@types/lodash-es/-/lodash-es-4.17.12.tgz"
  integrity sha512-0NgftHUcV4v34VhXm8QBSftKVXtbkBG3ViCjs6+eJ5a6y6Mi/jiFGPc1sC7QK+9BFhWrURE3EOggmWaSxL9OzQ==
  dependencies:
    "@types/lodash" "*"

"@types/lodash@*", "@types/lodash@^4.17.20":
  version "4.17.20"
  resolved "https://registry.npmjs.org/@types/lodash/-/lodash-4.17.20.tgz"
  integrity sha512-H3MHACvFUEiujabxhaI/ImO6gUrd8oOurg7LQtS7mbwIXA/cUqWrvBsaeJ23aZEPk1TAYkurjfMbSELfoCXlGA==

"@types/web-bluetooth@^0.0.16":
  version "0.0.16"
  resolved "https://registry.npmjs.org/@types/web-bluetooth/-/web-bluetooth-0.0.16.tgz"
  integrity sha512-oh8q2Zc32S6gd/j50GowEjKLoOVOwHP/bWVjKJInBwQqdOYMdPrf1oVlelTlyfFK3CKxL1uahMDAr+vy8T7yMQ==

"@vitejs/plugin-vue@^6.0.1":
  version "6.0.1"
  resolved "https://registry.npmjs.org/@vitejs/plugin-vue/-/plugin-vue-6.0.1.tgz"
  integrity sha512-+MaE752hU0wfPFJEUAIxqw18+20euHHdxVtMvbFcOEpjEyfqXH/5DCoTHiVJ0J29EhTJdoTkjEv5YBKU9dnoTw==
  dependencies:
    "@rolldown/pluginutils" "1.0.0-beta.29"

"@vue/compiler-core@3.5.22":
  version "3.5.22"
  resolved "https://registry.npmjs.org/@vue/compiler-core/-/compiler-core-3.5.22.tgz"
  integrity sha512-jQ0pFPmZwTEiRNSb+i9Ow/I/cHv2tXYqsnHKKyCQ08irI2kdF5qmYedmF8si8mA7zepUFmJ2hqzS8CQmNOWOkQ==
  dependencies:
    "@babel/parser" "^7.28.4"
    "@vue/shared" "3.5.22"
    entities "^4.5.0"
    estree-walker "^2.0.2"
    source-map-js "^1.2.1"

"@vue/compiler-dom@3.5.22":
  version "3.5.22"
  resolved "https://registry.npmjs.org/@vue/compiler-dom/-/compiler-dom-3.5.22.tgz"
  integrity sha512-W8RknzUM1BLkypvdz10OVsGxnMAuSIZs9Wdx1vzA3mL5fNMN15rhrSCLiTm6blWeACwUwizzPVqGJgOGBEN/hA==
  dependencies:
    "@vue/compiler-core" "3.5.22"
    "@vue/shared" "3.5.22"

"@vue/compiler-sfc@3.5.22":
  version "3.5.22"
  resolved "https://registry.npmjs.org/@vue/compiler-sfc/-/compiler-sfc-3.5.22.tgz"
  integrity sha512-tbTR1zKGce4Lj+JLzFXDq36K4vcSZbJ1RBu8FxcDv1IGRz//Dh2EBqksyGVypz3kXpshIfWKGOCcqpSbyGWRJQ==
  dependencies:
    "@babel/parser" "^7.28.4"
    "@vue/compiler-core" "3.5.22"
    "@vue/compiler-dom" "3.5.22"
    "@vue/compiler-ssr" "3.5.22"
    "@vue/shared" "3.5.22"
    estree-walker "^2.0.2"
    magic-string "^0.30.19"
    postcss "^8.5.6"
    source-map-js "^1.2.1"

"@vue/compiler-ssr@3.5.22":
  version "3.5.22"
  resolved "https://registry.npmjs.org/@vue/compiler-ssr/-/compiler-ssr-3.5.22.tgz"
  integrity sha512-GdgyLvg4R+7T8Nk2Mlighx7XGxq/fJf9jaVofc3IL0EPesTE86cP/8DD1lT3h1JeZr2ySBvyqKQJgbS54IX1Ww==
  dependencies:
    "@vue/compiler-dom" "3.5.22"
    "@vue/shared" "3.5.22"

"@vue/devtools-api@^6.6.4":
  version "6.6.4"
  resolved "https://registry.npmjs.org/@vue/devtools-api/-/devtools-api-6.6.4.tgz"
  integrity sha512-sGhTPMuXqZ1rVOk32RylztWkfXTRhuS7vgAKv0zjqk8gbsHkJ7xfFf+jbySxt7tWObEJwyKaHMikV/WGDiQm8g==

"@vue/reactivity@3.5.22":
  version "3.5.22"
  resolved "https://registry.npmjs.org/@vue/reactivity/-/reactivity-3.5.22.tgz"
  integrity sha512-f2Wux4v/Z2pqc9+4SmgZC1p73Z53fyD90NFWXiX9AKVnVBEvLFOWCEgJD3GdGnlxPZt01PSlfmLqbLYzY/Fw4A==
  dependencies:
    "@vue/shared" "3.5.22"

"@vue/runtime-core@3.5.22":
  version "3.5.22"
  resolved "https://registry.npmjs.org/@vue/runtime-core/-/runtime-core-3.5.22.tgz"
  integrity sha512-EHo4W/eiYeAzRTN5PCextDUZ0dMs9I8mQ2Fy+OkzvRPUYQEyK9yAjbasrMCXbLNhF7P0OUyivLjIy0yc6VrLJQ==
  dependencies:
    "@vue/reactivity" "3.5.22"
    "@vue/shared" "3.5.22"

"@vue/runtime-dom@3.5.22":
  version "3.5.22"
  resolved "https://registry.npmjs.org/@vue/runtime-dom/-/runtime-dom-3.5.22.tgz"
  integrity sha512-Av60jsryAkI023PlN7LsqrfPvwfxOd2yAwtReCjeuugTJTkgrksYJJstg1e12qle0NarkfhfFu1ox2D+cQotww==
  dependencies:
    "@vue/reactivity" "3.5.22"
    "@vue/runtime-core" "3.5.22"
    "@vue/shared" "3.5.22"
    csstype "^3.1.3"

"@vue/server-renderer@3.5.22":
  version "3.5.22"
  resolved "https://registry.npmjs.org/@vue/server-renderer/-/server-renderer-3.5.22.tgz"
  integrity sha512-gXjo+ao0oHYTSswF+a3KRHZ1WszxIqO7u6XwNHqcqb9JfyIL/pbWrrh/xLv7jeDqla9u+LK7yfZKHih1e1RKAQ==
  dependencies:
    "@vue/compiler-ssr" "3.5.22"
    "@vue/shared" "3.5.22"

"@vue/shared@3.5.22":
  version "3.5.22"
  resolved "https://registry.npmjs.org/@vue/shared/-/shared-3.5.22.tgz"
  integrity sha512-F4yc6palwq3TT0u+FYf0Ns4Tfl9GRFURDN2gWG7L1ecIaS/4fCIuFOjMTnCyjsu/OK6vaDKLCrGAa+KvvH+h4w==

"@vueuse/core@^9.1.0":
  version "9.13.0"
  resolved "https://registry.npmjs.org/@vueuse/core/-/core-9.13.0.tgz"
  integrity sha512-pujnclbeHWxxPRqXWmdkKV5OX4Wk4YeK7wusHqRwU0Q7EFusHoqNA/aPhB6KCh9hEqJkLAJo7bb0Lh9b+OIVzw==
  dependencies:
    "@types/web-bluetooth" "^0.0.16"
    "@vueuse/metadata" "9.13.0"
    "@vueuse/shared" "9.13.0"
    vue-demi "*"

"@vueuse/metadata@9.13.0":
  version "9.13.0"
  resolved "https://registry.npmjs.org/@vueuse/metadata/-/metadata-9.13.0.tgz"
  integrity sha512-gdU7TKNAUVlXXLbaF+ZCfte8BjRJQWPCa2J55+7/h+yDtzw3vOoGQDRXzI6pyKyo6bXFT5/QoPE4hAknExjRLQ==

"@vueuse/shared@9.13.0":
  version "9.13.0"
  resolved "https://registry.npmjs.org/@vueuse/shared/-/shared-9.13.0.tgz"
  integrity sha512-UrnhU+Cnufu4S6JLCPZnkWh0WwZGUp72ktOF2DFptMlOs3TOdVv8xJN53zhHGARmVOsz5KqOls09+J1NR6sBKw==
  dependencies:
    vue-demi "*"

ansis@=4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/ansis/-/ansis-4.2.0.tgz"
  integrity sha512-HqZ5rWlFjGiV0tDm3UxxgNRqsOTniqoKZu0pIAfh7TZQMGuZK+hH0drySty0si0QXj1ieop4+SkSfPZBPPkHig==

async-validator@^4.2.5:
  version "4.2.5"
  resolved "https://registry.npmjs.org/async-validator/-/async-validator-4.2.5.tgz"
  integrity sha512-7HhHjtERjqlNbZtqNqy2rckN/SpOOlmDliet+lP7k+eKZEjPk3DgyeU9lIXLdeLz0uBbbVp+9Qdow9wJWgwwfg==

csstype@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz"
  integrity sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==

dayjs@^1.11.13:
  version "1.11.18"
  resolved "https://registry.npmjs.org/dayjs/-/dayjs-1.11.18.tgz"
  integrity sha512-zFBQ7WFRvVRhKcWoUh+ZA1g2HVgUbsZm9sbddh8EC5iv93sui8DVVz1Npvz+r6meo9VKfa8NyLWBsQK1VvIKPA==

debug@~4.3.1, debug@~4.3.2:
  version "4.3.7"
  resolved "https://registry.npmjs.org/debug/-/debug-4.3.7.tgz"
  integrity sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ==
  dependencies:
    ms "^2.1.3"

detect-libc@^2.0.3:
  version "2.1.2"
  resolved "https://registry.npmjs.org/detect-libc/-/detect-libc-2.1.2.tgz"
  integrity sha512-Btj2BOOO83o3WyH59e8MgXsxEQVcarkUOpEYrubB0urwnN10yQ364rsiByU11nZlqWYZm05i/of7io4mzihBtQ==

element-plus@^2.11.4:
  version "2.11.4"
  resolved "https://registry.npmjs.org/element-plus/-/element-plus-2.11.4.tgz"
  integrity sha512-sLq+Ypd0cIVilv8wGGMEGvzRVBBsRpJjnAS5PsI/1JU1COZXqzH3N1UYMUc/HCdvdjf6dfrBy80Sj7KcACsT7w==
  dependencies:
    "@ctrl/tinycolor" "^3.4.1"
    "@element-plus/icons-vue" "^2.3.1"
    "@floating-ui/dom" "^1.0.1"
    "@popperjs/core" "npm:@sxzz/popperjs-es@^2.11.7"
    "@types/lodash" "^4.17.20"
    "@types/lodash-es" "^4.17.12"
    "@vueuse/core" "^9.1.0"
    async-validator "^4.2.5"
    dayjs "^1.11.13"
    escape-html "^1.0.3"
    lodash "^4.17.21"
    lodash-es "^4.17.21"
    lodash-unified "^1.0.3"
    memoize-one "^6.0.0"
    normalize-wheel-es "^1.2.0"

engine.io-client@~6.6.1:
  version "6.6.3"
  resolved "https://registry.npmjs.org/engine.io-client/-/engine.io-client-6.6.3.tgz"
  integrity sha512-T0iLjnyNWahNyv/lcjS2y4oE358tVS/SYQNxYXGAJ9/GLgH4VCvOQ/mhTjqU88mLZCQgiG8RIegFHYCdVC+j5w==
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    debug "~4.3.1"
    engine.io-parser "~5.2.1"
    ws "~8.17.1"
    xmlhttprequest-ssl "~2.1.1"

engine.io-parser@~5.2.1:
  version "5.2.3"
  resolved "https://registry.npmjs.org/engine.io-parser/-/engine.io-parser-5.2.3.tgz"
  integrity sha512-HqD3yTBfnBxIrbnM1DoD6Pcq8NECnh8d4As1Qgh0z5Gg3jRRIqijury0CL3ghu/edArpUYiYqQiDUQBIs4np3Q==

entities@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/entities/-/entities-4.5.0.tgz"
  integrity sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==

escape-html@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz"
  integrity sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==

estree-walker@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/estree-walker/-/estree-walker-2.0.2.tgz"
  integrity sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==

fdir@^6.5.0:
  version "6.5.0"
  resolved "https://registry.npmjs.org/fdir/-/fdir-6.5.0.tgz"
  integrity sha512-tIbYtZbucOs0BRGqPJkshJUYdL+SDH7dVM8gjy+ERp3WAUjLEFJE+02kanyHtwjWOnwrKYBiwAmM0p4kLJAnXg==

lightningcss-win32-x64-msvc@1.30.2:
  version "1.30.2"
  resolved "https://registry.npmjs.org/lightningcss-win32-x64-msvc/-/lightningcss-win32-x64-msvc-1.30.2.tgz"
  integrity sha512-5g1yc73p+iAkid5phb4oVFMB45417DkRevRbt/El/gKXJk4jid+vPFF/AXbxn05Aky8PapwzZrdJShv5C0avjw==

lightningcss@^1.30.1:
  version "1.30.2"
  resolved "https://registry.npmjs.org/lightningcss/-/lightningcss-1.30.2.tgz"
  integrity sha512-utfs7Pr5uJyyvDETitgsaqSyjCb2qNRAtuqUeWIAKztsOYdcACf2KtARYXg2pSvhkt+9NfoaNY7fxjl6nuMjIQ==
  dependencies:
    detect-libc "^2.0.3"
  optionalDependencies:
    lightningcss-android-arm64 "1.30.2"
    lightningcss-darwin-arm64 "1.30.2"
    lightningcss-darwin-x64 "1.30.2"
    lightningcss-freebsd-x64 "1.30.2"
    lightningcss-linux-arm-gnueabihf "1.30.2"
    lightningcss-linux-arm64-gnu "1.30.2"
    lightningcss-linux-arm64-musl "1.30.2"
    lightningcss-linux-x64-gnu "1.30.2"
    lightningcss-linux-x64-musl "1.30.2"
    lightningcss-win32-arm64-msvc "1.30.2"
    lightningcss-win32-x64-msvc "1.30.2"

lodash-es@*, lodash-es@^4.17.21:
  version "4.17.21"
  resolved "https://registry.npmjs.org/lodash-es/-/lodash-es-4.17.21.tgz"
  integrity sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==

lodash-unified@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/lodash-unified/-/lodash-unified-1.0.3.tgz"
  integrity sha512-WK9qSozxXOD7ZJQlpSqOT+om2ZfcT4yO+03FuzAHD0wF6S0l0090LRPDx3vhTTLZ8cFKpBn+IOcVXK6qOcIlfQ==

lodash@*, lodash@^4.17.21:
  version "4.17.21"
  resolved "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

magic-string@^0.30.19:
  version "0.30.19"
  resolved "https://registry.npmjs.org/magic-string/-/magic-string-0.30.19.tgz"
  integrity sha512-2N21sPY9Ws53PZvsEpVtNuSW+ScYbQdp4b9qUaL+9QkHUrGFKo56Lg9Emg5s9V/qrtNBmiR01sYhUOwu3H+VOw==
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.5"

memoize-one@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/memoize-one/-/memoize-one-6.0.0.tgz"
  integrity sha512-rkpe71W0N0c0Xz6QD0eJETuWAJGnJ9afsl1srmwPrI+yBCkge5EycXXbYRyvL29zZVUWQCY7InPRCv3GDXuZNw==

ms@^2.1.3:
  version "2.1.3"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

nanoid@^3.3.11:
  version "3.3.11"
  resolved "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz"
  integrity sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==

normalize-wheel-es@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/normalize-wheel-es/-/normalize-wheel-es-1.2.0.tgz"
  integrity sha512-Wj7+EJQ8mSuXr2iWfnujrimU35R2W4FAErEyTmJoJ7ucwTn2hOUSsRehMb5RSYkxXGTM7Y9QpvPmp++w5ftoJw==

picocolors@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz"
  integrity sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==

"picomatch@^3 || ^4", picomatch@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-4.0.3.tgz"
  integrity sha512-5gTmgEY/sqK6gFXLIsQNH19lWb4ebPDLA4SdLP7dsWkIXHWlG66oPuVvXSGFPppYZz8ZDZq0dYYrbHfBCVUb1Q==

postcss@^8.5.6:
  version "8.5.6"
  resolved "https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz"
  integrity sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==
  dependencies:
    nanoid "^3.3.11"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

rolldown@1.0.0-beta.41:
  version "1.0.0-beta.41"
  resolved "https://registry.npmjs.org/rolldown/-/rolldown-1.0.0-beta.41.tgz"
  integrity sha512-U+NPR0Bkg3wm61dteD2L4nAM1U9dtaqVrpDXwC36IKRHpEO/Ubpid4Nijpa2imPchcVNHfxVFwSSMJdwdGFUbg==
  dependencies:
    "@oxc-project/types" "=0.93.0"
    "@rolldown/pluginutils" "1.0.0-beta.41"
    ansis "=4.2.0"
  optionalDependencies:
    "@rolldown/binding-android-arm64" "1.0.0-beta.41"
    "@rolldown/binding-darwin-arm64" "1.0.0-beta.41"
    "@rolldown/binding-darwin-x64" "1.0.0-beta.41"
    "@rolldown/binding-freebsd-x64" "1.0.0-beta.41"
    "@rolldown/binding-linux-arm-gnueabihf" "1.0.0-beta.41"
    "@rolldown/binding-linux-arm64-gnu" "1.0.0-beta.41"
    "@rolldown/binding-linux-arm64-musl" "1.0.0-beta.41"
    "@rolldown/binding-linux-x64-gnu" "1.0.0-beta.41"
    "@rolldown/binding-linux-x64-musl" "1.0.0-beta.41"
    "@rolldown/binding-openharmony-arm64" "1.0.0-beta.41"
    "@rolldown/binding-wasm32-wasi" "1.0.0-beta.41"
    "@rolldown/binding-win32-arm64-msvc" "1.0.0-beta.41"
    "@rolldown/binding-win32-ia32-msvc" "1.0.0-beta.41"
    "@rolldown/binding-win32-x64-msvc" "1.0.0-beta.41"

socket.io-client@^4.8.1:
  version "4.8.1"
  resolved "https://registry.npmjs.org/socket.io-client/-/socket.io-client-4.8.1.tgz"
  integrity sha512-hJVXfu3E28NmzGk8o1sHhN3om52tRvwYeidbj7xKy2eIIse5IoKX3USlS6Tqt3BHAtflLIkCQBkzVrEEfWUyYQ==
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    debug "~4.3.2"
    engine.io-client "~6.6.1"
    socket.io-parser "~4.2.4"

socket.io-parser@~4.2.4:
  version "4.2.4"
  resolved "https://registry.npmjs.org/socket.io-parser/-/socket.io-parser-4.2.4.tgz"
  integrity sha512-/GbIKmo8ioc+NIWIhwdecY0ge+qVBSMdgxGygevmdHj24bsfgtCmcUUcQ5ZzcylGFHsN3k4HB4Cgkl96KVnuew==
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    debug "~4.3.1"

source-map-js@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz"
  integrity sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==

tinyglobby@^0.2.15:
  version "0.2.15"
  resolved "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.15.tgz"
  integrity sha512-j2Zq4NyQYG5XMST4cbs02Ak8iJUdxRM0XI5QyxXuZOzKOINmWurp3smXu3y5wDcJrptwpSjgXHzIQxR0omXljQ==
  dependencies:
    fdir "^6.5.0"
    picomatch "^4.0.3"

"vite@^5.0.0 || ^6.0.0 || ^7.0.0", "vite@npm:rolldown-vite@7.1.14":
  version "7.1.14"
  resolved "https://registry.npmjs.org/rolldown-vite/-/rolldown-vite-7.1.14.tgz"
  integrity sha512-eSiiRJmovt8qDJkGyZuLnbxAOAdie6NCmmd0NkTC0RJI9duiSBTfr8X2mBYJOUFzxQa2USaHmL99J9uMxkjCyw==
  dependencies:
    "@oxc-project/runtime" "0.92.0"
    fdir "^6.5.0"
    lightningcss "^1.30.1"
    picomatch "^4.0.3"
    postcss "^8.5.6"
    rolldown "1.0.0-beta.41"
    tinyglobby "^0.2.15"
  optionalDependencies:
    fsevents "~2.3.3"

vue-demi@*:
  version "0.14.10"
  resolved "https://registry.npmjs.org/vue-demi/-/vue-demi-0.14.10.tgz"
  integrity sha512-nMZBOwuzabUO0nLgIcc6rycZEebF6eeUfaiQx9+WSk8e29IbLvPU9feI6tqW4kTo3hvoYAJkMh8n8D0fuISphg==

vue-router@^4.5.1:
  version "4.5.1"
  resolved "https://registry.npmjs.org/vue-router/-/vue-router-4.5.1.tgz"
  integrity sha512-ogAF3P97NPm8fJsE4by9dwSYtDwXIY1nFY9T6DyQnGHd1E2Da94w9JIolpe42LJGIl0DwOHBi8TcRPlPGwbTtw==
  dependencies:
    "@vue/devtools-api" "^6.6.4"

"vue@^3.0.0-0 || ^2.6.0", vue@^3.2.0, vue@^3.2.25, vue@^3.5.22, vue@3.5.22:
  version "3.5.22"
  resolved "https://registry.npmjs.org/vue/-/vue-3.5.22.tgz"
  integrity sha512-toaZjQ3a/G/mYaLSbV+QsQhIdMo9x5rrqIpYRObsJ6T/J+RyCSFwN2LHNVH9v8uIcljDNa3QzPVdv3Y6b9hAJQ==
  dependencies:
    "@vue/compiler-dom" "3.5.22"
    "@vue/compiler-sfc" "3.5.22"
    "@vue/runtime-dom" "3.5.22"
    "@vue/server-renderer" "3.5.22"
    "@vue/shared" "3.5.22"

ws@~8.17.1:
  version "8.17.1"
  resolved "https://registry.npmjs.org/ws/-/ws-8.17.1.tgz"
  integrity sha512-6XQFvXTkbfUOZOKKILFG1PDK2NDQs4azKQl26T0YS5CxqWLgXajbPZ+h4gZekJyRqFU8pvnbAbbs/3TgRPy+GQ==

xmlhttprequest-ssl@~2.1.1:
  version "2.1.2"
  resolved "https://registry.npmjs.org/xmlhttprequest-ssl/-/xmlhttprequest-ssl-2.1.2.tgz"
  integrity sha512-TEU+nJVUUnA4CYJFLvK5X9AOeH4KvDvhIfm0vV1GaQRtchnG0hgK5p8hw/xjv8cunWYCsiPCSDzObPyhEwq3KQ==
