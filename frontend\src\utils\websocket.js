// WebSocket工具，用于前端与后端的实时通信
import { ref, onMounted, onUnmounted } from 'vue';
import { io } from 'socket.io-client';

export function useWebSocket(url = 'ws://localhost:8000/ws') {
  const socket = ref(null);
  const connected = ref(false);
  const reconnectAttempts = ref(0);
  const maxReconnectAttempts = 5;
  const reconnectDelay = 3000; // 重连延迟（毫秒）
  
  // 消息队列，用于存储连接建立前需要发送的消息
  const messageQueue = ref([]);
  
  // 事件监听器映射
  const eventListeners = ref(new Map());
  
  // 初始化WebSocket连接
  const connect = () => {
    try {
      // 创建Socket.io连接
      socket.value = io(url, {
        transports: ['websocket'],
        autoConnect: true,
        reconnection: true,
        reconnectionAttempts: maxReconnectAttempts,
        reconnectionDelay: reconnectDelay
      });
      
      // 连接成功
      socket.value.on('connect', () => {
        console.log('WebSocket连接成功');
        connected.value = true;
        reconnectAttempts.value = 0;
        
        // 发送队列中的消息
        while (messageQueue.value.length > 0) {
          const { event, data } = messageQueue.value.shift();
          sendMessage(event, data);
        }
      });
      
      // 连接断开
      socket.value.on('disconnect', (reason) => {
        console.log('WebSocket连接断开:', reason);
        connected.value = false;
      });
      
      // 连接错误
      socket.value.on('connect_error', (error) => {
        console.error('WebSocket连接错误:', error);
        reconnectAttempts.value++;
        
        if (reconnectAttempts.value >= maxReconnectAttempts) {
          console.error('达到最大重连次数，停止重连');
        }
      });
      
    } catch (error) {
      console.error('WebSocket初始化失败:', error);
    }
  };
  
  // 断开连接
  const disconnect = () => {
    if (socket.value) {
      socket.value.disconnect();
      socket.value = null;
      connected.value = false;
    }
  };
  
  // 发送消息
  const sendMessage = (event, data) => {
    if (socket.value && connected.value) {
      socket.value.emit(event, data);
    } else {
      // 如果未连接，将消息加入队列
      messageQueue.value.push({ event, data });
      console.log('WebSocket未连接，消息已加入队列');
    }
  };
  
  // 监听事件
  const onMessage = (event, callback) => {
    if (!socket.value) {
      // 如果socket未初始化，先存储监听器
      if (!eventListeners.value.has(event)) {
        eventListeners.value.set(event, []);
      }
      eventListeners.value.get(event).push(callback);
      return;
    }
    
    // 注册事件监听器
    socket.value.on(event, callback);
  };
  
  // 移除事件监听
  const offMessage = (event, callback) => {
    if (socket.value) {
      socket.value.off(event, callback);
    }
    
    // 从存储的监听器中移除
    if (eventListeners.value.has(event) && callback) {
      const listeners = eventListeners.value.get(event);
      const index = listeners.indexOf(callback);
      if (index !== -1) {
        listeners.splice(index, 1);
      }
    }
  };
  
  // 处理设备连接状态更新
  const handleDeviceStatusUpdate = (callback) => {
    onMessage('device_status_update', callback);
  };
  
  // 处理设备镜像流数据
  const handleDeviceFrame = (callback) => {
    onMessage('device_frame', callback);
  };
  
  // 处理脚本执行状态更新
  const handleScriptStatusUpdate = (callback) => {
    onMessage('script_status_update', callback);
  };
  
  // 处理脚本日志
  const handleScriptLog = (callback) => {
    onMessage('script_log', callback);
  };
  
  // 扫描设备
  const scanDevices = () => {
    sendMessage('scan_devices');
  };
  
  // 连接设备
  const connectDevice = (deviceId) => {
    sendMessage('connect_device', { deviceId });
  };
  
  // 断开设备连接
  const disconnectDevice = (deviceId) => {
    sendMessage('disconnect_device', { deviceId });
  };
  
  // 开始设备镜像
  const startMirroring = (deviceId) => {
    sendMessage('start_mirroring', { deviceId });
  };
  
  // 停止设备镜像
  const stopMirroring = (deviceId) => {
    sendMessage('stop_mirroring', { deviceId });
  };
  
  // 执行脚本
  const executeScript = (scriptId, deviceIds = []) => {
    sendMessage('execute_script', { scriptId, deviceIds });
  };
  
  // 停止脚本
  const stopScript = (scriptId) => {
    sendMessage('stop_script', { scriptId });
  };
  
  // 组件挂载时初始化连接
  onMounted(() => {
    connect();
  });
  
  // 组件卸载时断开连接
  onUnmounted(() => {
    disconnect();
  });
  
  return {
    socket,
    connected,
    connect,
    disconnect,
    sendMessage,
    onMessage,
    offMessage,
    handleDeviceStatusUpdate,
    handleDeviceFrame,
    handleScriptStatusUpdate,
    handleScriptLog,
    scanDevices,
    connectDevice,
    disconnectDevice,
    startMirroring,
    stopMirroring,
    executeScript,
    stopScript
  };
}

// 导出一个默认的WebSocket实例
let defaultWebSocket = null;

export function getDefaultWebSocket() {
  if (!defaultWebSocket) {
    defaultWebSocket = useWebSocket();
  }
  return defaultWebSocket;
}

export default useWebSocket;