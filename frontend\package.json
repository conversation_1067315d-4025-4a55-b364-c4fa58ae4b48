{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"element-plus": "^2.11.4", "socket.io-client": "^4.8.1", "vue": "^3.5.22", "vue-router": "^4.5.1", "@element-plus/icons-vue": "^2.3.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.1", "vite": "npm:rolldown-vite@7.1.14"}, "resolutions": {"vite": "npm:rolldown-vite@7.1.14"}}