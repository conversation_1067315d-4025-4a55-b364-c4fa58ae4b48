import subprocess
import re
import sys
import time

def get_adb_devices():
    """获取已连接的adb设备列表"""
    try:
        # 运行adb devices命令
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, check=True)
        # 解析输出结果
        output = result.stdout
        # 使用正则表达式匹配设备信息
        devices = []
        for line in output.split('\n'):
            match = re.match(r'([\w\d:.]+)\s+(device|offline)', line.strip())
            if match:
                device_id = match.group(1)
                device_status = match.group(2)
                devices.append({"id": device_id, "status": device_status})
        return devices
    except subprocess.CalledProcessError as e:
        print(f"获取设备列表失败: {e}")
        return []
    except FileNotFoundError:
        print("未找到adb命令，请确保已安装Android SDK并将adb添加到系统PATH中")
        return []

def get_device_size(device_id):
    """获取设备的屏幕尺寸"""
    try:
        # 使用wm size命令获取屏幕尺寸
        result = subprocess.run(['adb', '-s', device_id, 'shell', 'wm', 'size'], 
                               capture_output=True, text=True, check=True)
        output = result.stdout.strip()
        # 解析输出，格式通常是：Physical size: 1080x2340
        if 'Physical size:' in output:
            # 获取Physical size行
            physical_size_line = None
            for line in output.split('\n'):
                if 'Physical size:' in line:
                    physical_size_line = line
                    break
            
            if physical_size_line:
                size_str = physical_size_line.split('Physical size: ')[1].strip()
                # 确保只获取尺寸部分，移除可能的其他内容
                if 'x' in size_str:
                    parts = size_str.split('x')
                    if len(parts) >= 2:
                        # 只取x前面的数字和x后面的第一个数字
                        width_str = re.search(r'\d+', parts[0]).group()
                        height_str = re.search(r'\d+', parts[1]).group()
                        
                        try:
                            width = int(width_str)
                            height = int(height_str)
                            return {'width': width, 'height': height}
                        except ValueError:
                            print(f"无法转换屏幕尺寸为整数: {size_str}")
                            return None
            
            print(f"无法解析屏幕尺寸信息: {output}")
            return None
        else:
            print(f"无法解析屏幕尺寸信息: {output}")
            return None
    except subprocess.CalledProcessError as e:
        print(f"获取屏幕尺寸失败: {e}")
        return None

def click_phone(device_id, x, y, action_name="点击"):
    """使用adb命令执行点击操作"""
    try:
        print(f"在设备 {device_id} 上{action_name}，坐标: ({x}, {y})")
        # 执行adb shell input tap命令点击指定坐标
        result = subprocess.run(['adb', '-s', device_id, 'shell', 'input', 'tap', str(x), str(y)], 
                               capture_output=True, text=True, check=True)
        
        # 检查输出结果
        if result.returncode == 0:
            print(f"已成功{action_name}")
            return True
        else:
            print(f"{action_name}时返回非零状态码: {result.returncode}")
            print(f"错误输出: {result.stderr}")
            return False
    except subprocess.CalledProcessError as e:
        print(f"{action_name}失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    except FileNotFoundError:
        print("未找到adb命令，请确保已安装Android SDK并将adb添加到系统PATH中")
        return False

def touch_swip(device_id, x1, y1, x2, y2, time):
    """使用adb命令执行滑动操作"""
    try:
        print(f"在设备 {device_id} 上执行滑动操作，从坐标 ({x1}, {y1}) 到 ({x2}, {y2})，持续时间: {time}毫秒")
        # 执行adb shell input swipe命令执行滑动操作
        result = subprocess.run(['adb', '-s', device_id, 'shell', 'input', 'swipe', str(x1), str(y1), str(x2), str(y2), str(time)], 
                               capture_output=True, text=True, check=True)
        
        # 检查输出结果
        if result.returncode == 0:
            print("滑动操作成功")
            return True
        else:
            print(f"滑动操作时返回非零状态码: {result.returncode}")
            print(f"错误输出: {result.stderr}")
            return False
    except subprocess.CalledProcessError as e:
        print(f"滑动操作失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    except FileNotFoundError:
        print("未找到adb命令，请确保已安装Android SDK并将adb添加到系统PATH中")
        return False

def input_text(device_id, text):
    """使用adb命令向设备输入文本"""
    try:
        print(f"在设备 {device_id} 上输入文本: {text}")
        # 对文本进行转义，处理特殊字符
        # 在adb shell input text中，空格需要用%s表示，其他特殊字符也需要转义
        escaped_text = ''
        for char in text:
            if char == ' ':
                escaped_text += '%s'
            elif char in '"\'+*[](){}^|\&;`~<>':
                # 对特殊字符进行转义
                escaped_text += '\\' + char
            else:
                escaped_text += char
        
        # 执行adb shell input text命令输入文本
        result = subprocess.run(['adb', '-s', device_id, 'shell', 'input', 'text', escaped_text], 
                               capture_output=True, text=True, check=True)
        
        # 检查输出结果
        if result.returncode == 0:
            print("文本输入成功")
            return True
        else:
            print(f"文本输入时返回非零状态码: {result.returncode}")
            print(f"错误输出: {result.stderr}")
            return False
    except subprocess.CalledProcessError as e:
        print(f"文本输入失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    except FileNotFoundError:
        print("未找到adb命令，请确保已安装Android SDK并将adb添加到系统PATH中")
        return False

def open_app(device_id, appname):
    """使用adb命令打开指定的app"""
    try:
        print(f"正在设备 {device_id} 上打开app: {appname}")
        # 执行adb shell命令打开app
        result = subprocess.run(['adb', '-s', device_id, 'shell', 'monkey', '-p', appname, 
                                '-c', 'android.intent.category.LAUNCHER', '1'], 
                               capture_output=True, text=True, check=True)
        # 检查输出结果
        if result.returncode == 0:
            print(f"app {appname} 已成功打开")
        else:
            print(f"打开app {appname} 时返回非零状态码: {result.returncode}")
            print(f"错误输出: {result.stderr}")
    except subprocess.CalledProcessError as e:
        print(f"打开app {appname} 失败: {e}")
        print(f"错误输出: {e.stderr}")
    except FileNotFoundError:
        print("未找到adb命令，请确保已安装Android SDK并将adb添加到系统PATH中")

def connect_to_device(device_id):
    """使用指定的设备ID连接到设备的shell"""
    try:
        print(f"正在连接到设备: {device_id}...")
        # 执行adb -s 设备号 shell命令
        subprocess.run(['adb', '-s', device_id, 'shell'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"连接设备失败: {e}")
    except KeyboardInterrupt:
        print("已退出shell")

def main():
    """主函数"""
    # 获取设备列表
    devices = get_adb_devices()
    
    if not devices:
        print("未检测到任何已连接的设备")
        sys.exit(1)
    
    # 显示设备列表和屏幕尺寸
    print("已连接的设备列表:")
    for i, device in enumerate(devices, 1):
        device_id = device['id']
        # 获取屏幕尺寸
        size_info = get_device_size(device_id)
        if size_info:
            size_text = f"，屏幕尺寸: {size_info['width']}x{size_info['height']}"
        else:
            size_text = "，无法获取屏幕尺寸"
        print(f"{i}. 设备ID: {device_id}, 状态: {device['status']}{size_text}")
    
    # 选择设备
    if len(devices) == 1:
        device_id = devices[0]['id']
        print(f"只有一个设备，自动选择 {device_id}")
    else:
        try:
            choice = int(input("请选择要操作的设备编号 (1-" + str(len(devices)) + "): "))
            if 1 <= choice <= len(devices):
                device_id = devices[choice - 1]['id']
            else:
                print("无效的选择")
                sys.exit(1)
        except ValueError:
            print("请输入有效的数字")
            sys.exit(1)
    
    # 提供操作选项
    print(f"\n设备 {device_id} 的操作选项:")
    print("1. 连接到设备shell")
    print("2. 打开抖音app")
    print("3. 执行点击搜索")
    print("4. 执行点击点赞")
    print("5. 执行点击收藏")
    print("6. 执行点击关注")
    print("7. 执行滑动操作")
    print("8. 抖音自动刷视频")
    try:
        option = int(input("请选择操作 (1-8): "))
        if option == 1:
            connect_to_device(device_id)
        elif option == 2:
            open_app(device_id, "com.ss.android.ugc.aweme")
        elif option == 3:
            click_phone(device_id, 660, 120, "执行点击操作")
            text = input("请输入要发送的文本: ")
            input_text(device_id, text)
            click_phone(device_id, 660, 120, "执行点击操作")
        elif option == 4:
            click_phone(device_id, 664, 900, "执行点击操作")
        elif option == 5:
            click_phone(device_id, 664, 1180, "执行点击操作")
        elif option == 6:
            click_phone(device_id, 664, 823, "执行点击操作")
        elif option == 7:
            touch_swip(device_id, 528, 800, 528, 410, 200)
        elif option == 8:
            open_app(device_id, "com.ss.android.ugc.aweme")
            time.sleep(9)
            click_phone(device_id, 660, 120, "执行点击操作")
            text = input("请输入要发送的文本: ")
            input_text(device_id, text)
            click_phone(device_id, 660, 120, "执行点击操作")
            time.sleep(3)
            # 点击视频
            click_phone(device_id, 200, 200, "执行点击操作")
            time.sleep(3)
            click_phone(device_id, 200, 600, "执行点击操作")
            
            # 设置循环次数或使用while True进行无限循环
            try:
                loop_count = int(input("请输入要自动刷的视频数量 (-1表示无限循环): "))
                if loop_count == -1:
                    print("开始无限循环自动刷视频，按Ctrl+C停止...")
                    while True:
                        # 等待视频加载
                        print("等待视频加载和播放...")
                        time.sleep(3)  # 等待3秒让视频加载
                        # 点击点赞
                        click_phone(device_id, 664, 800, "执行点击操作")
                        time.sleep(3)
                        # 点击收藏
                        click_phone(device_id, 664, 1090, "执行点击操作")
                        time.sleep(3)
                        # 点击关注
                        click_phone(device_id, 664, 734, "执行点击操作")
                        # 等待2秒
                        time.sleep(2)
                        # 评论
                        click_phone(device_id, 660, 950, "执行点击操作")
                        time.sleep(3)
                        click_phone(device_id, 200, 1550, "执行点击操作")
                        text = input("请输入要发送的文本: ")
                        input_text(device_id, text)
                        time.sleep(3)
                        click_phone(device_id, 660, 910, "执行点击操作")
                        time.sleep(3)
                        click_phone(device_id, 360, 200, "执行点击操作")
                        time.sleep(3)
                        # 滑动到下一个视频
                        touch_swip(device_id, 300, 1046, 300, 318, 200)
                else:
                    print(f"开始自动刷 {loop_count} 个视频...")
                    for i in range(loop_count):
                        print(f"正在处理第 {i+1}/{loop_count} 个视频")
                        # 等待视频加载
                        print("等待视频加载和播放...")
                        time.sleep(3)  # 等待3秒让视频加载
                        
                         # 点击点赞
                        click_phone(device_id, 664, 800, "执行点击操作")
                        time.sleep(3)
                        # 点击收藏
                        click_phone(device_id, 664, 1090, "执行点击操作")
                        time.sleep(3)
                        # 点击关注
                        click_phone(device_id, 664, 734, "执行点击操作")
                        # 等待2秒
                        time.sleep(2)
                        # 评论
                        click_phone(device_id, 660, 950, "执行点击操作")
                        time.sleep(3)
                        click_phone(device_id, 200, 1550, "执行点击操作")
                        text = input("请输入要发送的文本: ")
                        input_text(device_id, text)
                        time.sleep(3)
                        click_phone(device_id, 660, 910, "执行点击操作")
                        time.sleep(3)
                        click_phone(device_id, 360, 200, "执行点击操作")
                        time.sleep(3)
                        # 滑动到下一个视频
                        touch_swip(device_id, 300, 1046, 300, 318, 200)
                print("自动刷视频任务完成")
            except KeyboardInterrupt:
                print("\n已停止自动刷视频")
            except ValueError:
                print("请输入有效的数字")
        else:
            print("无效的选择")
            sys.exit(1)
    except ValueError:
        print("请输入有效的数字")
        sys.exit(1)

if __name__ == "__main__":
    main()